from typing import Any, Dict, List, Optional, Union

from sqlalchemy.orm import Session

from app import schemas
from app.models.question import Question, DifficultyLevel, QuestionType


def get(db: Session, id: int) -> Optional[Question]:
    return db.query(Question).filter(Question.id == id).first()


def get_multi(
    db: Session,
    *,
    skip: int = 0,
    limit: int = 100,
    course_id: Optional[int] = None,
    difficulty: Optional[DifficultyLevel] = None,
    question_type: Optional[QuestionType] = None,
    topic: Optional[str] = None
) -> list[Question]:
    query = db.query(Question)

    if course_id is not None:
        query = query.filter(Question.course_id == course_id)

    if difficulty is not None:
        query = query.filter(Question.difficulty == difficulty)

    if question_type is not None:
        query = query.filter(Question.question_type == question_type)

    if topic is not None:
        query = query.filter(Question.topic == topic)

    return query.offset(skip).limit(limit).all()


def create(
    db: Session, *, obj_in: schemas.QuestionCreate, created_by_id: int
) -> Question:
    db_obj = Question(
        content=obj_in.content,
        question_type=obj_in.question_type,
        difficulty=obj_in.difficulty,
        topic=obj_in.topic,
        options=obj_in.options,
        answer=obj_in.answer,
        explanation=obj_in.explanation,
        media_url=obj_in.media_url,
        is_active=obj_in.is_active,
        course_id=obj_in.course_id,
        course_name=obj_in.course_name,
        created_by_id=created_by_id,
        mcq_job_id=obj_in.mcq_job_id,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update(
    db: Session, *, db_obj: Question, obj_in: Union[schemas.QuestionUpdate, Dict[str, Any]]
) -> Question:
    if isinstance(obj_in, dict):
        update_data = obj_in
    else:
        update_data = obj_in.model_dump(exclude_unset=True)
    for field in update_data:
        if field in update_data:
            setattr(db_obj, field, update_data[field])
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete(db: Session, *, id: int) -> Question:
    obj = db.query(Question).get(id)
    db.delete(obj)
    db.commit()
    return obj


def get_by_course(
    db: Session, *, course_id: int, skip: int = 0, limit: int = 100
) -> List[Question]:
    return (
        db.query(Question)
        .filter(Question.course_id == course_id)
        .offset(skip)
        .limit(limit)
        .all()
    )


def get_by_mcq_job(
    db: Session, *, mcq_job_id: int, skip: int = 0, limit: int = 100
) -> List[Question]:
    """Get questions generated by a specific MCQ generation job."""
    return (
        db.query(Question)
        .filter(Question.mcq_job_id == mcq_job_id)
        .offset(skip)
        .limit(limit)
        .all()
    )
