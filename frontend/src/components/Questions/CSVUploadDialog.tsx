import React, { useState, useRef } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Alert,
  LinearProgress,
  Chip,
  Divider,
  IconButton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  GetApp as DownloadIcon,
  Description as FileIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  CloudUpload as UploadIcon,
} from '@mui/icons-material';
import { useQueryClient, useQuery } from '@tanstack/react-query';

import {
  uploadCSVQuestions,
  downloadCSVTemplate,
  uploadCSVQuestionsWithImages,
  CSVUploadResponse,
  QuestionType
} from '../../api/questions';
import { getCourses, Course } from '../../api/courses';

interface CSVUploadDialogProps {
  open: boolean;
  onClose: () => void;
}

const CSVUploadDialog: React.FC<CSVUploadDialogProps> = ({ open, onClose }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<CSVUploadResponse | null>(null);
  const [downloadingTemplate, setDownloadingTemplate] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<number | ''>('');
  const [selectedQuestionType, setSelectedQuestionType] = useState<QuestionType | ''>('');
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imageMapping, setImageMapping] = useState<Record<string, string>>({});
  const [showImageUpload, setShowImageUpload] = useState(false);
  const [imageMappingMode, setImageMappingMode] = useState<'row' | 'content'>('row');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const queryClient = useQueryClient();

  // Fetch courses
  const { data: courses = [] } = useQuery({
    queryKey: ['courses'],
    queryFn: () => getCourses(),
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setUploadResult(null);
    }
  };

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedImages(prev => [...prev, ...files]); // Append to existing images

    // Initialize image mapping for new files
    const newMapping: Record<string, string> = { ...imageMapping };
    files.forEach((file, index) => {
      if (!newMapping[file.name]) {
        if (imageMappingMode === 'row') {
          newMapping[file.name] = (selectedImages.length + index + 1).toString();
        } else {
          newMapping[file.name] = ''; // Empty for content mode
        }
      }
    });
    setImageMapping(newMapping);
  };

  const updateImageMapping = (filename: string, questionRef: string) => {
    setImageMapping(prev => ({
      ...prev,
      [filename]: questionRef
    }));
  };

  const removeImage = (filename: string) => {
    setSelectedImages(prev => prev.filter(file => file.name !== filename));
    setImageMapping(prev => {
      const newMapping = { ...prev };
      delete newMapping[filename];
      return newMapping;
    });
  };

  const handleUpload = async () => {
    if (!selectedFile || !selectedCourse || !selectedQuestionType) {
      setUploadResult({
        success: false,
        total_rows: 0,
        created_questions: 0,
        validation_errors: ['Please select a file, course, and question type'],
        creation_errors: [],
        questions: [],
      });
      return;
    }

    setUploading(true);
    try {
      let result: CSVUploadResponse;

      if (selectedImages.length > 0) {
        // Upload CSV with images
        result = await uploadCSVQuestionsWithImages(
          selectedFile,
          selectedCourse as number,
          selectedQuestionType as QuestionType,
          selectedImages,
          imageMapping
        );
      } else {
        // Upload CSV only
        result = await uploadCSVQuestions(
          selectedFile,
          selectedCourse as number,
          selectedQuestionType as QuestionType
        );
      }

      setUploadResult(result);

      if (result.success) {
        // Invalidate questions cache to refresh the list
        queryClient.invalidateQueries({ queryKey: ['questions'] });
      }
    } catch (error: any) {
      setUploadResult({
        success: false,
        total_rows: 0,
        created_questions: 0,
        validation_errors: [error.message || 'Upload failed'],
        creation_errors: [],
        questions: [],
      });
    } finally {
      setUploading(false);
    }
  };

  const createManualTemplate = (questionType: QuestionType) => {
    let templateContent = '';

    if (questionType === 'multiple_choice') {
      templateContent = `content,difficulty,answer,topic,explanation,option_a,option_b,option_c,option_d
"What is the capital of France?",easy,C,Geography,"Paris is the capital and largest city of France.",London,Berlin,Paris,Madrid
"Which planet is closest to the Sun?",medium,A,Astronomy,"Mercury is the innermost planet in our solar system.",Mercury,Venus,Earth,Mars`;
    } else {
      templateContent = `content,difficulty,answer,topic,explanation,option_a,option_b,option_c,option_d
"Define photosynthesis",medium,"The process by which plants convert light energy into chemical energy",Biology,"Photosynthesis is crucial for plant survival and oxygen production.",,,,
"What is 2 + 2?",easy,4,Mathematics,"Basic addition",,,,`;
    }

    const blob = new Blob([templateContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `questions_template_${questionType}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  const handleDownloadTemplate = async () => {
    if (!selectedQuestionType) {
      setUploadResult({
        success: false,
        total_rows: 0,
        created_questions: 0,
        validation_errors: ['Please select a question type first'],
        creation_errors: [],
        questions: [],
      });
      return;
    }

    setDownloadingTemplate(true);
    try {
      const blob = await downloadCSVTemplate(selectedQuestionType as QuestionType);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `questions_template_${selectedQuestionType}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error('Failed to download template from server, using fallback:', error);
      // Fallback to manual template creation
      createManualTemplate(selectedQuestionType as QuestionType);
    } finally {
      setDownloadingTemplate(false);
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setUploadResult(null);
    setUploading(false);
    setSelectedCourse('');
    setSelectedQuestionType('');
    setSelectedImages([]);
    setImageMapping({});
    setShowImageUpload(false);
    setImageMappingMode('row');
    onClose();
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file && file.name.toLowerCase().endsWith('.csv')) {
      setSelectedFile(file);
      setUploadResult(null);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          m: { xs: 1, sm: 2 },
          maxHeight: { xs: '95vh', sm: '90vh' }
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">Upload Questions from CSV</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Alert severity="info" sx={{ mb: 2 }}>
            Upload a CSV file containing questions to bulk import them into the system.
            First select the course and question type, then download the template or upload your file.
          </Alert>

          {/* Course and Question Type Selection */}
          <Box sx={{
            display: 'flex',
            gap: 2,
            mb: 2,
            flexDirection: { xs: 'column', sm: 'row' }
          }}>
            <FormControl fullWidth>
              <InputLabel>Course</InputLabel>
              <Select
                value={selectedCourse}
                label="Course"
                onChange={(e: SelectChangeEvent<number | ''>) => setSelectedCourse(e.target.value)}
              >
                {courses.map((course) => (
                  <MenuItem key={course.id} value={course.id}>
                    <Box sx={{
                      display: 'flex',
                      flexDirection: { xs: 'column', sm: 'row' },
                      gap: { xs: 0, sm: 1 }
                    }}>
                      <Typography variant="body2">{course.name}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        ({course.code})
                      </Typography>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Question Type</InputLabel>
              <Select
                value={selectedQuestionType}
                label="Question Type"
                onChange={(e: SelectChangeEvent<QuestionType | ''>) => setSelectedQuestionType(e.target.value)}
              >
                <MenuItem value="multiple_choice">Multiple Choice</MenuItem>
                <MenuItem value="flashcard">Flashcard</MenuItem>
                <MenuItem value="open_ended">Open Ended</MenuItem>
              </Select>
            </FormControl>
          </Box>

          <Box sx={{
            display: 'flex',
            gap: 1,
            mb: 2,
            flexDirection: { xs: 'column', sm: 'row' },
            '& > button': {
              minWidth: { xs: '100%', sm: 'auto' }
            }
          }}>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleDownloadTemplate}
              disabled={downloadingTemplate || !selectedQuestionType}
              size={{ xs: 'medium', sm: 'medium' }}
            >
              {downloadingTemplate ? 'Downloading...' : 'Download Template'}
            </Button>

            <Button
              variant="outlined"
              onClick={() => setShowImageUpload(!showImageUpload)}
              disabled={!selectedQuestionType}
              size={{ xs: 'medium', sm: 'medium' }}
            >
              {showImageUpload ? 'Hide' : 'Add'} Images
            </Button>
          </Box>

          {/* Image Upload Section */}
          {showImageUpload && (
            <Box sx={{ mb: 2, p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Optional: Upload Images for Questions
              </Typography>

              <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="outlined"
                  component="label"
                  startIcon={<UploadIcon />}
                  size="small"
                >
                  Add Images
                  <input
                    ref={imageInputRef}
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageSelect}
                    style={{ display: 'none' }}
                  />
                </Button>

                <FormControl size="small" sx={{ minWidth: 150 }}>
                  <InputLabel>Mapping Mode</InputLabel>
                  <Select
                    value={imageMappingMode}
                    label="Mapping Mode"
                    onChange={(e: SelectChangeEvent<'row' | 'content'>) => {
                      setImageMappingMode(e.target.value as 'row' | 'content');
                      // Reset mappings when mode changes
                      const newMapping: Record<string, string> = {};
                      selectedImages.forEach((file, index) => {
                        if (e.target.value === 'row') {
                          newMapping[file.name] = (index + 1).toString();
                        } else {
                          newMapping[file.name] = '';
                        }
                      });
                      setImageMapping(newMapping);
                    }}
                  >
                    <MenuItem value="row">Row Number</MenuItem>
                    <MenuItem value="content">Question Text</MenuItem>
                  </Select>
                </FormControl>
              </Box>

              {selectedImages.length > 0 && (
                <Box>
                  <Typography variant="body2" gutterBottom>
                    Selected Images ({selectedImages.length}):
                  </Typography>

                  <Box sx={{ maxHeight: 300, overflowY: 'auto' }}>
                    {selectedImages.map((file, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          mb: 1,
                          p: 1,
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 1,
                          flexWrap: { xs: 'wrap', sm: 'nowrap' }
                        }}
                      >
                        <Chip
                          label={file.name}
                          onDelete={() => removeImage(file.name)}
                          size="small"
                          sx={{ minWidth: 120, maxWidth: { xs: '100%', sm: 200 } }}
                        />
                        <Typography variant="caption" sx={{ mr: 1, display: { xs: 'none', sm: 'block' } }}>
                          →
                        </Typography>
                        <Box sx={{ flex: 1, minWidth: { xs: '100%', sm: 200 } }}>
                          <input
                            type="text"
                            placeholder={imageMappingMode === 'row' ? 'Row number (1, 2, 3...)' : 'Question text'}
                            value={imageMapping[file.name] || ''}
                            onChange={(e) => updateImageMapping(file.name, e.target.value)}
                            style={{
                              width: '100%',
                              padding: '8px 12px',
                              border: '1px solid #ccc',
                              borderRadius: '4px',
                              fontSize: '14px',
                              fontFamily: 'inherit'
                            }}
                          />
                        </Box>
                      </Box>
                    ))}
                  </Box>

                  <Alert severity="info" sx={{ mt: 2 }}>
                    <Typography variant="caption">
                      {imageMappingMode === 'row'
                        ? 'Enter the row number (1, 2, 3...) from your CSV file for each image.'
                        : 'Enter the exact question text from your CSV file for each image.'
                      }
                    </Typography>
                  </Alert>
                </Box>
              )}
            </Box>
          )}

          <Accordion sx={{ mb: 2 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle2">CSV Format Requirements</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" gutterBottom>
                <strong>Required Columns:</strong>
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="content"
                    secondary="The question text"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="difficulty"
                    secondary="One of: easy, medium, hard"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="answer"
                    secondary="The correct answer (for multiple choice, use A, B, C, or D)"
                  />
                </ListItem>
              </List>

              <Typography variant="body2" gutterBottom sx={{ mt: 2 }}>
                <strong>Optional Columns:</strong>
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="topic"
                    secondary="Question topic/category"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="explanation"
                    secondary="Answer explanation"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="option_a, option_b, option_c, option_d"
                    secondary="Multiple choice options (option_a and option_b required for multiple choice)"
                  />
                </ListItem>
              </List>

              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Note:</strong> Course and question type are selected in the form above,
                  not in the CSV file. The template will be customized based on your selection.
                </Typography>
              </Alert>
            </AccordionDetails>
          </Accordion>
        </Box>

        <Paper
          sx={{
            p: 3,
            border: '2px dashed',
            borderColor: 'divider',
            textAlign: 'center',
            cursor: 'pointer',
            '&:hover': {
              borderColor: 'primary.main',
              bgcolor: 'action.hover',
            },
          }}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept=".csv"
            onChange={handleFileSelect}
            style={{ display: 'none' }}
          />

          <CloudUploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
          
          {selectedFile ? (
            <Box>
              <Typography variant="h6" gutterBottom>
                Selected File
              </Typography>
              <Chip
                icon={<FileIcon />}
                label={selectedFile.name}
                color="primary"
                sx={{ mb: 1 }}
              />
              <Typography variant="body2" color="text.secondary">
                Size: {(selectedFile.size / 1024).toFixed(1)} KB
              </Typography>
            </Box>
          ) : (
            <Box>
              <Typography variant="h6" gutterBottom>
                Drop CSV file here or click to browse
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Supported format: .csv (max 10MB)
              </Typography>
            </Box>
          )}
        </Paper>

        {uploading && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" gutterBottom>
              Processing CSV file...
            </Typography>
            <LinearProgress />
          </Box>
        )}

        {uploadResult && (
          <Box sx={{ mt: 3 }}>
            <Alert 
              severity={uploadResult.success ? 'success' : 'error'} 
              sx={{ mb: 2 }}
            >
              {uploadResult.success 
                ? `Successfully created ${uploadResult.created_questions} questions from ${uploadResult.total_rows} rows`
                : 'Upload failed with errors'
              }
            </Alert>

            {uploadResult.validation_errors.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  <ErrorIcon sx={{ fontSize: 16, mr: 1, verticalAlign: 'middle' }} />
                  Validation Errors:
                </Typography>
                <List dense>
                  {uploadResult.validation_errors.map((error, index) => (
                    <ListItem key={index}>
                      <ListItemText primary={error} />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}

            {uploadResult.creation_errors.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  <ErrorIcon sx={{ fontSize: 16, mr: 1, verticalAlign: 'middle' }} />
                  Creation Errors:
                </Typography>
                <List dense>
                  {uploadResult.creation_errors.map((error, index) => (
                    <ListItem key={index}>
                      <ListItemText primary={error} />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>
          {uploadResult?.success ? 'Close' : 'Cancel'}
        </Button>
        <Button
          variant="contained"
          onClick={handleUpload}
          disabled={!selectedFile || !selectedCourse || !selectedQuestionType || uploading}
          startIcon={<CloudUploadIcon />}
        >
          {uploading ? 'Uploading...' : 'Upload CSV'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CSVUploadDialog;
