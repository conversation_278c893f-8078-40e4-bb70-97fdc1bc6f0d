import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css';

interface MathMarkdownProps {
  children: string;
  className?: string;
}

/**
 * A component that renders markdown content with math expressions
 * using KaTeX for math rendering.
 *
 * Supports both inline math with $ and block math with $$.
 *
 * Example:
 * Inline math: $E = mc^2$
 * Block math: $$\int_{a}^{b} f(x) dx = F(b) - F(a)$$
 */
const MathMarkdown: React.FC<MathMarkdownProps> = ({ children, className }) => {
  // Handle errors in KaTeX rendering
  const katexOptions = {
    throwOnError: false,
    strict: false,
    trust: true,
    output: 'html',
    macros: {
      // Common math macros
      "\\R": "\\mathbb{R}",
      "\\N": "\\mathbb{N}",
      "\\Z": "\\mathbb{Z}",
      "\\Q": "\\mathbb{Q}",
      "\\C": "\\mathbb{C}",
    }
  };

  // Process content to ensure proper line breaks
  const processedContent = children
    // Replace single newlines with two newlines for proper markdown paragraphs
    .replace(/(?<!\n)\n(?!\n)/g, '\n\n')
    // Ensure math blocks have proper spacing
    .replace(/\$\$(.*?)\$\$/gs, (match) => {
      // Add newlines around display math if not already present
      if (!match.startsWith('\n\n')) {
        match = '\n\n' + match;
      }
      if (!match.endsWith('\n\n')) {
        match = match + '\n\n';
      }
      return match;
    });

  return (
    <div className={className}>
      <ReactMarkdown
        remarkPlugins={[remarkMath]}
        rehypePlugins={[[rehypeKatex, katexOptions]]}
        components={{
          // Add custom styling for code blocks
          code: ({ node, inline, className, children, ...props }) => {
            return (
              <code className={className} {...props}>
                {children}
              </code>
            );
          },
          // Ensure paragraphs have proper spacing
          p: ({ children }) => (
            <p style={{ marginBottom: '1em' }}>{children}</p>
          ),
        }}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
};

export default MathMarkdown;
