import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  CircularProgress,
  Alert,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Pagination,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Search as SearchIcon,
  QuestionAnswer as QuestionIcon,
  CloudUpload as UploadIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getQuestions,
  deleteQuestion,
  Question,
  QuestionType,
  DifficultyLevel
} from '../../api/questions';
import { getCourses } from '../../api/courses';
import { useAuth } from '../../contexts/AuthContext';
import { useContextualNavigation } from '../../hooks/useAdminContext';
import CSVUploadDialog from '../../components/Questions/CSVUploadDialog';
import BulkImageUploadDialog from '../../components/Questions/BulkImageUploadDialog';

const QuestionsList: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { getPath } = useContextualNavigation();
  const queryClient = useQueryClient();

  const isAdmin = user?.role === 'admin';
  const isTutor = user?.role === 'tutor';
  const canManageQuestions = isAdmin || isTutor;

  // Get courseId from location state if available
  const initialCourseId = location.state?.courseId;

  // State for pagination
  const [page, setPage] = useState(1);
  const [itemsPerPage] = useState(12);

  // State for filtering
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCourse, setSelectedCourse] = useState<string>(initialCourseId?.toString() || '');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('');
  const [selectedTopic, setSelectedTopic] = useState<string>('');

  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [questionToDelete, setQuestionToDelete] = useState<Question | null>(null);

  // State for CSV upload dialog
  const [csvUploadDialogOpen, setCsvUploadDialogOpen] = useState(false);

  // State for bulk image upload dialog
  const [bulkImageUploadDialogOpen, setBulkImageUploadDialogOpen] = useState(false);

  // Fetch questions data
  const {
    data: questions = [],
    isLoading: isLoadingQuestions,
    error: questionsError
  } = useQuery({
    queryKey: ['questions', selectedCourse ? parseInt(selectedCourse) : undefined, selectedDifficulty, selectedType, selectedTopic],
    queryFn: () => getQuestions(
      selectedCourse ? parseInt(selectedCourse) : undefined,
      selectedDifficulty as DifficultyLevel || undefined,
      selectedType as QuestionType || undefined,
      selectedTopic || undefined
    )
  });

  // Fetch courses data for filtering
  const {
    data: courses = [],
    isLoading: isLoadingCourses
  } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses
  });

  // Delete question mutation
  const deleteMutation = useMutation({
    mutationFn: (id: number) => deleteQuestion(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['questions'] });
      setDeleteDialogOpen(false);
      setQuestionToDelete(null);
    }
  });

  // Handle page change
  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
    window.scrollTo(0, 0);
  };

  // Handle course filter change
  const handleCourseChange = (event: SelectChangeEvent) => {
    setSelectedCourse(event.target.value);
    setPage(1);
  };

  // Handle type filter change
  const handleTypeChange = (event: SelectChangeEvent) => {
    setSelectedType(event.target.value);
    setPage(1);
  };

  // Handle difficulty filter change
  const handleDifficultyChange = (event: SelectChangeEvent) => {
    setSelectedDifficulty(event.target.value);
    setPage(1);
  };

  // Handle topic filter change
  const handleTopicChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedTopic(event.target.value);
    setPage(1);
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (question: Question) => {
    setQuestionToDelete(question);
    setDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setQuestionToDelete(null);
  };

  // Confirm delete
  const handleDeleteConfirm = () => {
    if (questionToDelete) {
      deleteMutation.mutate(questionToDelete.id);
    }
  };

  // Filter questions based on search term
  const filteredQuestions = questions.filter((question) => {
    return question.content.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Get course name by ID
  const getCourseName = (courseId: number): string => {
    const course = courses.find(c => c.id === courseId);
    return course ? course.name : 'Unknown Course';
  };

  // Calculate pagination
  const indexOfLastItem = page * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredQuestions.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredQuestions.length / itemsPerPage);

  // Get difficulty color
  const getDifficultyColor = (difficulty: DifficultyLevel) => {
    switch (difficulty) {
      case 'easy':
        return 'success';
      case 'medium':
        return 'warning';
      case 'hard':
        return 'error';
      default:
        return 'default';
    }
  };

  // Truncate text
  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  if (isLoadingQuestions) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (questionsError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading questions: {(questionsError as Error).message}
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', sm: 'center' },
        mb: 3,
        flexDirection: { xs: 'column', sm: 'row' },
        gap: { xs: 2, sm: 0 }
      }}>
        <Typography variant="h4" component="h1" sx={{ fontSize: { xs: '1.75rem', sm: '2.125rem' } }}>
          Questions
        </Typography>

        {canManageQuestions && (
          <Box sx={{
            display: 'flex',
            gap: 1,
            flexDirection: { xs: 'column', sm: 'row' },
            width: { xs: '100%', sm: 'auto' },
            '& > button': {
              minWidth: { xs: '100%', sm: 'auto' }
            }
          }}>
            {isAdmin && (
              <>
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<UploadIcon />}
                  onClick={() => setCsvUploadDialogOpen(true)}
                  size="medium"
                >
                  <Box component="span" sx={{ display: { xs: 'none', sm: 'inline' } }}>Upload </Box>CSV
                </Button>
                <Button
                  variant="outlined"
                  color="secondary"
                  startIcon={<UploadIcon />}
                  onClick={() => setBulkImageUploadDialogOpen(true)}
                  size="medium"
                >
                  <Box component="span" sx={{ display: { xs: 'none', sm: 'inline' } }}>Bulk </Box>Images
                </Button>
              </>
            )}
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              component={RouterLink}
              to={getPath("/questions/new")}
              state={{ courseId: selectedCourse ? parseInt(selectedCourse) : undefined }}
              size="medium"
            >
              Add Question
            </Button>
          </Box>
        )}
      </Box>

      {/* Filters */}
      <Paper sx={{ p: { xs: 1.5, sm: 2 }, mb: 3 }}>
        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          gap: { xs: 1.5, sm: 2 },
          flexWrap: 'wrap'
        }}>
          <TextField
            label="Search Questions"
            variant="outlined"
            size="small"
            fullWidth
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{
              order: { xs: 1, md: 0 },
              minWidth: { xs: '100%', md: 'auto' }
            }}
          />

          <FormControl
            size="small"
            sx={{
              minWidth: { xs: '100%', sm: 200, md: 200 },
              order: { xs: 2, md: 0 }
            }}
          >
            <InputLabel id="course-filter-label">Course</InputLabel>
            <Select
              labelId="course-filter-label"
              id="course-filter"
              value={selectedCourse}
              label="Course"
              onChange={handleCourseChange}
              disabled={isLoadingCourses}
            >
              <MenuItem value="">All Courses</MenuItem>
              {courses.map((course) => (
                <MenuItem key={course.id} value={course.id.toString()}>
                  <Box sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    gap: { xs: 0, sm: 1 }
                  }}>
                    <Typography variant="body2">{course.name}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      ({course.code})
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl
            size="small"
            sx={{
              minWidth: { xs: '48%', sm: 150, md: 150 },
              order: { xs: 3, md: 0 }
            }}
          >
            <InputLabel id="type-filter-label">Type</InputLabel>
            <Select
              labelId="type-filter-label"
              id="type-filter"
              value={selectedType}
              label="Type"
              onChange={handleTypeChange}
            >
              <MenuItem value="">All Types</MenuItem>
              <MenuItem value="multiple_choice">
                <Box sx={{ display: { xs: 'block', sm: 'none' } }}>MC</Box>
                <Box sx={{ display: { xs: 'none', sm: 'block' } }}>Multiple Choice</Box>
              </MenuItem>
              <MenuItem value="flashcard">Flashcard</MenuItem>
              <MenuItem value="open_ended">
                <Box sx={{ display: { xs: 'block', sm: 'none' } }}>Open</Box>
                <Box sx={{ display: { xs: 'none', sm: 'block' } }}>Open Ended</Box>
              </MenuItem>
            </Select>
          </FormControl>

          <FormControl
            size="small"
            sx={{
              minWidth: { xs: '48%', sm: 150, md: 150 },
              order: { xs: 4, md: 0 }
            }}
          >
            <InputLabel id="difficulty-filter-label">Difficulty</InputLabel>
            <Select
              labelId="difficulty-filter-label"
              id="difficulty-filter"
              value={selectedDifficulty}
              label="Difficulty"
              onChange={handleDifficultyChange}
            >
              <MenuItem value="">All</MenuItem>
              <MenuItem value="easy">Easy</MenuItem>
              <MenuItem value="medium">Medium</MenuItem>
              <MenuItem value="hard">Hard</MenuItem>
            </Select>
          </FormControl>

          <TextField
            label="Topic"
            variant="outlined"
            size="small"
            sx={{
              minWidth: { xs: '100%', sm: 150, md: 150 },
              order: { xs: 5, md: 0 }
            }}
            value={selectedTopic}
            onChange={handleTopicChange}
            placeholder="Filter by topic"
          />
        </Box>
      </Paper>

      {filteredQuestions.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <QuestionIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No questions found
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            {searchTerm || selectedCourse || selectedType || selectedDifficulty
              ? 'Try adjusting your filters'
              : 'No questions have been added yet'}
          </Typography>
          {canManageQuestions && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              component={RouterLink}
              to={getPath("/questions/new")}
              state={{ courseId: selectedCourse ? parseInt(selectedCourse) : undefined }}
              sx={{ mt: 2 }}
            >
              Add Question
            </Button>
          )}
        </Paper>
      ) : (
        <>
          <Grid container spacing={3}>
            {currentItems.map((question) => (
              <Grid item xs={12} sm={6} md={4} key={question.id}>
                <Card
                  variant="outlined"
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    '&:hover': {
                      boxShadow: 3
                    }
                  }}
                >
                  <CardContent sx={{ flexGrow: 1, p: { xs: 1.5, sm: 2 } }}>
                    <Box sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      mb: 1,
                      flexWrap: 'wrap',
                      gap: 1
                    }}>
                      <Chip
                        label={question.question_type.replace('_', ' ')}
                        size="small"
                        color="primary"
                        variant="outlined"
                        sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
                      />
                      <Chip
                        label={question.difficulty}
                        size="small"
                        color={getDifficultyColor(question.difficulty)}
                        variant="outlined"
                        sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
                      />
                    </Box>

                    <Typography
                      variant="subtitle1"
                      component="div"
                      sx={{
                        mb: 1,
                        minHeight: '3em',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                      }}
                    >
                      {truncateText(question.content, 100)}
                    </Typography>

                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Course: {getCourseName(question.course_id)}
                    </Typography>

                    {question.topic && (
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Topic: {question.topic}
                      </Typography>
                    )}

                    {question.question_type === 'multiple_choice' && question.options && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Options:
                        </Typography>
                        <Box sx={{ pl: 2 }}>
                          {Object.entries(question.options).slice(0, 2).map(([key, value]) => (
                            <Typography key={key} variant="body2" sx={{ mb: 0.5 }}>
                              {key}: {truncateText(value, 30)}
                            </Typography>
                          ))}
                          {Object.keys(question.options).length > 2 && (
                            <Typography variant="body2" color="text.secondary">
                              +{Object.keys(question.options).length - 2} more options
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    )}
                  </CardContent>

                  <Divider />

                  <CardActions sx={{
                    p: { xs: 1, sm: 1.5 },
                    flexWrap: 'wrap',
                    gap: { xs: 0.5, sm: 1 }
                  }}>
                    <Button
                      size="small"
                      component={RouterLink}
                      to={getPath(`/questions/${question.id}`)}
                      startIcon={<ViewIcon />}
                      sx={{
                        fontSize: { xs: '0.75rem', sm: '0.875rem' },
                        minWidth: { xs: 'auto', sm: '64px' }
                      }}
                    >
                      <Box component="span" sx={{ display: { xs: 'none', sm: 'inline' } }}>View</Box>
                      <Box component="span" sx={{ display: { xs: 'inline', sm: 'none' } }}>
                        <ViewIcon fontSize="small" />
                      </Box>
                    </Button>

                    {canManageQuestions && (
                      <>
                        <Button
                          size="small"
                          component={RouterLink}
                          to={getPath(`/questions/${question.id}/edit`)}
                          startIcon={<EditIcon />}
                          sx={{
                            fontSize: { xs: '0.75rem', sm: '0.875rem' },
                            minWidth: { xs: 'auto', sm: '64px' }
                          }}
                        >
                          <Box component="span" sx={{ display: { xs: 'none', sm: 'inline' } }}>Edit</Box>
                          <Box component="span" sx={{ display: { xs: 'inline', sm: 'none' } }}>
                            <EditIcon fontSize="small" />
                          </Box>
                        </Button>

                        {(isAdmin || (isTutor && question.created_by_id === user?.id)) && (
                          <Button
                            size="small"
                            color="error"
                            startIcon={<DeleteIcon />}
                            onClick={() => handleDeleteClick(question)}
                            sx={{
                              fontSize: { xs: '0.75rem', sm: '0.875rem' },
                              minWidth: { xs: 'auto', sm: '64px' }
                            }}
                          >
                            <Box component="span" sx={{ display: { xs: 'none', sm: 'inline' } }}>Delete</Box>
                            <Box component="span" sx={{ display: { xs: 'inline', sm: 'none' } }}>
                              <DeleteIcon fontSize="small" />
                            </Box>
                          </Button>
                        )}
                      </>
                    )}
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>

          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handlePageChange}
                color="primary"
                size="large"
              />
            </Box>
          )}
        </>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Delete Question</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this question? This action cannot be undone.
          </DialogContentText>
          {questionToDelete && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Question:
              </Typography>
              <Typography variant="body1">
                {truncateText(questionToDelete.content, 150)}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* CSV Upload Dialog */}
      <CSVUploadDialog
        open={csvUploadDialogOpen}
        onClose={() => setCsvUploadDialogOpen(false)}
      />

      {/* Bulk Image Upload Dialog */}
      <BulkImageUploadDialog
        open={bulkImageUploadDialogOpen}
        onClose={() => setBulkImageUploadDialogOpen(false)}
      />
    </Box>
  );
};

export default QuestionsList;
